// 分页功能测试
// 验证分页组件和Hook的功能

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { usePagination, getRecommendedPageSize } from '../src/hooks/usePagination'
import Pagination from '../src/components/Pagination'
import PaginatedBookmarkList from '../src/components/PaginatedBookmarkList'
import type { Bookmark } from '../src/types'

// 模拟收藏数据生成器
const generateMockBookmarks = (count: number): Bookmark[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `bookmark-${index}`,
    title: `测试收藏 ${index + 1}`,
    url: `https://example.com/bookmark-${index}`,
    description: `这是第 ${index + 1} 个测试收藏的描述信息。`,
    category: index % 3 === 0 ? '技术' : index % 3 === 1 ? '生活' : '学习',
    tags: [`标签${index % 5}`, `类型${index % 3}`],
    favicon: `https://example.com/favicon-${index}.ico`,
    createdAt: new Date(Date.now() - index * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - index * 43200000).toISOString()
  }))
}

// 测试组件：用于测试usePagination Hook
const TestPaginationComponent: React.FC<{
  items: any[]
  pageSize?: number
  onPageChange?: (page: number) => void
}> = ({ items, pageSize = 10, onPageChange }) => {
  const {
    currentPage,
    totalPages,
    totalItems,
    currentItems,
    goToPage,
    goToNextPage,
    goToPrevPage
  } = usePagination(items, { initialPageSize: pageSize })

  React.useEffect(() => {
    if (onPageChange) {
      onPageChange(currentPage)
    }
  }, [currentPage, onPageChange])

  return (
    <div>
      <div data-testid="pagination-info">
        <span data-testid="current-page">{currentPage}</span>
        <span data-testid="total-pages">{totalPages}</span>
        <span data-testid="total-items">{totalItems}</span>
        <span data-testid="current-items-count">{currentItems.length}</span>
      </div>
      
      <div data-testid="current-items">
        {currentItems.map((item, index) => (
          <div key={item.id || index} data-testid={`item-${item.id || index}`}>
            {item.title || item.name || `Item ${index}`}
          </div>
        ))}
      </div>
      
      <div data-testid="pagination-controls">
        <button onClick={goToPrevPage} data-testid="prev-button">上一页</button>
        <button onClick={goToNextPage} data-testid="next-button">下一页</button>
        <button onClick={() => goToPage(1)} data-testid="first-button">第一页</button>
        <button onClick={() => goToPage(totalPages)} data-testid="last-button">最后一页</button>
      </div>
    </div>
  )
}

describe('分页功能测试', () => {
  beforeEach(() => {
    // 清除localStorage
    localStorage.clear()
    
    // 模拟浏览器环境
    Object.defineProperty(window, 'ResizeObserver', {
      writable: true,
      value: vi.fn().mockImplementation(() => ({
        observe: vi.fn(),
        unobserve: vi.fn(),
        disconnect: vi.fn(),
      })),
    })
  })

  describe('usePagination Hook', () => {
    it('应该正确初始化分页状态', () => {
      const items = generateMockBookmarks(25)
      
      render(<TestPaginationComponent items={items} pageSize={10} />)
      
      expect(screen.getByTestId('current-page')).toHaveTextContent('1')
      expect(screen.getByTestId('total-pages')).toHaveTextContent('3')
      expect(screen.getByTestId('total-items')).toHaveTextContent('25')
      expect(screen.getByTestId('current-items-count')).toHaveTextContent('10')
    })

    it('应该正确处理页面导航', async () => {
      const items = generateMockBookmarks(25)
      const onPageChange = vi.fn()
      
      render(<TestPaginationComponent items={items} pageSize={10} onPageChange={onPageChange} />)
      
      // 测试下一页
      fireEvent.click(screen.getByTestId('next-button'))
      await waitFor(() => {
        expect(screen.getByTestId('current-page')).toHaveTextContent('2')
      })
      
      // 测试上一页
      fireEvent.click(screen.getByTestId('prev-button'))
      await waitFor(() => {
        expect(screen.getByTestId('current-page')).toHaveTextContent('1')
      })
      
      // 测试最后一页
      fireEvent.click(screen.getByTestId('last-button'))
      await waitFor(() => {
        expect(screen.getByTestId('current-page')).toHaveTextContent('3')
      })
      
      // 测试第一页
      fireEvent.click(screen.getByTestId('first-button'))
      await waitFor(() => {
        expect(screen.getByTestId('current-page')).toHaveTextContent('1')
      })
    })

    it('应该正确处理空数据', () => {
      render(<TestPaginationComponent items={[]} pageSize={10} />)
      
      expect(screen.getByTestId('current-page')).toHaveTextContent('1')
      expect(screen.getByTestId('total-pages')).toHaveTextContent('1')
      expect(screen.getByTestId('total-items')).toHaveTextContent('0')
      expect(screen.getByTestId('current-items-count')).toHaveTextContent('0')
    })

    it('应该正确计算最后一页的项目数量', () => {
      const items = generateMockBookmarks(23) // 23个项目，每页10个，最后一页3个
      
      render(<TestPaginationComponent items={items} pageSize={10} />)
      
      // 跳转到最后一页
      fireEvent.click(screen.getByTestId('last-button'))
      
      expect(screen.getByTestId('current-page')).toHaveTextContent('3')
      expect(screen.getByTestId('current-items-count')).toHaveTextContent('3')
    })
  })

  describe('getRecommendedPageSize', () => {
    it('应该为不同视图模式返回正确的页面大小', () => {
      expect(getRecommendedPageSize('row')).toBe(20)
      expect(getRecommendedPageSize('compact')).toBe(12)
      expect(getRecommendedPageSize('card')).toBe(8)
    })
  })

  describe('Pagination组件', () => {
    it('应该正确渲染分页控件', () => {
      const onPageChange = vi.fn()
      
      render(
        <Pagination
          currentPage={2}
          totalPages={5}
          totalItems={50}
          pageSize={10}
          onPageChange={onPageChange}
        />
      )
      
      // 验证页码按钮存在
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      
      // 验证导航按钮存在
      expect(screen.getByTitle('上一页 (←)')).toBeInTheDocument()
      expect(screen.getByTitle('下一页 (→)')).toBeInTheDocument()
    })

    it('应该正确处理页码点击', () => {
      const onPageChange = vi.fn()
      
      render(
        <Pagination
          currentPage={1}
          totalPages={5}
          totalItems={50}
          pageSize={10}
          onPageChange={onPageChange}
        />
      )
      
      // 点击页码3
      fireEvent.click(screen.getByText('3'))
      expect(onPageChange).toHaveBeenCalledWith(3)
    })

    it('应该在第一页时禁用上一页按钮', () => {
      const onPageChange = vi.fn()
      
      render(
        <Pagination
          currentPage={1}
          totalPages={5}
          totalItems={50}
          pageSize={10}
          onPageChange={onPageChange}
        />
      )
      
      const prevButton = screen.getByTitle('上一页 (←)')
      expect(prevButton).toBeDisabled()
    })

    it('应该在最后一页时禁用下一页按钮', () => {
      const onPageChange = vi.fn()
      
      render(
        <Pagination
          currentPage={5}
          totalPages={5}
          totalItems={50}
          pageSize={10}
          onPageChange={onPageChange}
        />
      )
      
      const nextButton = screen.getByTitle('下一页 (→)')
      expect(nextButton).toBeDisabled()
    })
  })

  describe('PaginatedBookmarkList组件', () => {
    it('应该正确渲染收藏列表', () => {
      const bookmarks = generateMockBookmarks(15)
      const mockHandlers = {
        onEdit: vi.fn(),
        onDelete: vi.fn(),
        onClick: vi.fn()
      }
      
      render(
        <PaginatedBookmarkList
          bookmarks={bookmarks}
          viewMode="card"
          {...mockHandlers}
        />
      )
      
      // 验证收藏项目存在（卡片模式每页8个）
      expect(screen.getByText('测试收藏 1')).toBeInTheDocument()
      expect(screen.getByText('测试收藏 8')).toBeInTheDocument()
      
      // 验证分页控件存在
      expect(screen.getByText('共 15 条')).toBeInTheDocument()
    })

    it('应该在空状态时显示正确的提示', () => {
      const mockHandlers = {
        onEdit: vi.fn(),
        onDelete: vi.fn(),
        onClick: vi.fn()
      }
      
      render(
        <PaginatedBookmarkList
          bookmarks={[]}
          viewMode="card"
          {...mockHandlers}
        />
      )
      
      expect(screen.getByText('暂无收藏')).toBeInTheDocument()
      expect(screen.getByText(/还没有添加任何收藏/)).toBeInTheDocument()
    })

    it('应该在不同视图模式下正确工作', () => {
      const bookmarks = generateMockBookmarks(10)
      const mockHandlers = {
        onEdit: vi.fn(),
        onDelete: vi.fn(),
        onClick: vi.fn()
      }
      
      const { rerender } = render(
        <PaginatedBookmarkList
          bookmarks={bookmarks}
          viewMode="row"
          {...mockHandlers}
        />
      )
      
      // 行视图模式
      expect(screen.getByText('测试收藏 1')).toBeInTheDocument()
      
      // 切换到紧凑视图
      rerender(
        <PaginatedBookmarkList
          bookmarks={bookmarks}
          viewMode="compact"
          {...mockHandlers}
        />
      )
      
      expect(screen.getByText('测试收藏 1')).toBeInTheDocument()
      
      // 切换到卡片视图
      rerender(
        <PaginatedBookmarkList
          bookmarks={bookmarks}
          viewMode="card"
          {...mockHandlers}
        />
      )
      
      expect(screen.getByText('测试收藏 1')).toBeInTheDocument()
    })
  })

  describe('键盘导航', () => {
    it('应该响应键盘事件', async () => {
      const bookmarks = generateMockBookmarks(25)
      const mockHandlers = {
        onEdit: vi.fn(),
        onDelete: vi.fn(),
        onClick: vi.fn()
      }
      
      render(
        <PaginatedBookmarkList
          bookmarks={bookmarks}
          viewMode="card"
          enableKeyboardNavigation={true}
          {...mockHandlers}
        />
      )
      
      // 模拟右箭头键（下一页）
      fireEvent.keyDown(document, { key: 'ArrowRight' })
      
      await waitFor(() => {
        // 验证页面已切换（这里需要根据实际实现调整验证方式）
        expect(screen.getByText(/第 9-16 条/)).toBeInTheDocument()
      })
    })
  })
})
